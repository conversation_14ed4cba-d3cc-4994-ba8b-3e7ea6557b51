import csv
import io
import logging
import uuid
import zipfile
from decimal import Decimal
from typing import Any, Callable, Iterable, Optional, Type

from dateutil.parser import parse
from django.core.files.uploadedfile import UploadedFile
from django.http import HttpResponse
from import_export import resources, widgets
from tablib import Dataset

from nga.apps.import_export_data.consts import (
    AGREEMENT_PARTNER_OPERATORS_COLUMN_NAME,
    ALL_CHOICE_VALUE,
    CALCULATION_RESULTS_MAPPER,
    COUNTRIES_HEADERS,
    ENCODING_FORMAT,
    EXCHANGE_RATES_HEADERS,
    IMSI_COUNT_HEADERS,
    LIVE_AGREEMENTS_HEADERS,
    MONTHLY_AGGREGATIONS_HEADERS,
    OPERATORS_HEADERS,
    TRAFFIC_SEGMENTS_HEADERS,
)
from nga.apps.import_export_data.enums import EntityTypeEnum
from nga.core.enums import CallDestinationEnum, ServiceType<PERSON><PERSON>


def create_csv_file(
    data: Iterable[dict[str, Any]], entity_type: EntityTypeEnum, model_resource: Type[resources.ModelResource]
) -> UploadedFile:
    
    file = io.StringIO()
    model_fields = {v.column_name: v for v in model_resource.fields.values()}
    match entity_type:
        case entity_type.HISTORICAL_MONTHLY_AGGREGATIONS:
            headers = MONTHLY_AGGREGATIONS_HEADERS
        case entity_type.LIVE_AGREEMENTS:
            headers = LIVE_AGREEMENTS_HEADERS
        case entity_type.COUNTRIES:
            headers = COUNTRIES_HEADERS
        case entity_type.OPERATORS:
            headers = OPERATORS_HEADERS
        case entity_type.CALCULATED_TRAFFIC_RECORD:
            headers = list(CALCULATION_RESULTS_MAPPER.keys())
        case entity_type.EXCHANGE_RATES:
            headers = EXCHANGE_RATES_HEADERS
        case entity_type.TRAFFIC_SEGMENTS:
            headers = TRAFFIC_SEGMENTS_HEADERS
        case entity_type.IMSI_COUNT:
            headers = IMSI_COUNT_HEADERS
        case _:
            raise ValueError("Invalid entity type in use")
    csv_writer = csv.writer(file, delimiter=",")
    csv_writer.writerow(headers)

    def format_input_date(field_name: str, value: Optional[str]) -> Optional[str]:
        if (
            value
            and field_name in model_fields
            and isinstance(model_fields[field_name].widget, (widgets.DateWidget, widgets.DateTimeWidget))
        ):
            return str(parse(value).strftime(model_fields[field_name].widget.formats[0]))
        return value

    field_attr: Callable[[str], str] = (
        lambda f: CALCULATION_RESULTS_MAPPER[f]
        if entity_type == EntityTypeEnum.CALCULATED_TRAFFIC_RECORD
        else f.lower()
    )

    for entity in data:
        csv_writer.writerow([format_input_date(field, entity.get(field_attr(field))) for field in headers])

    bytes_file = io.BytesIO(file.getvalue().encode(ENCODING_FORMAT))

    return UploadedFile(
        file=bytes_file,
        name=f"{entity_type.name}_{uuid.uuid4()}.csv",
        content_type="text/csv",
    )


class CSVHttpResponseFromDataset(HttpResponse):
    CONTENT_TYPE = "text/csv"

    def __init__(self, dataset: Dataset, filename: str, *args: Any, **kwargs: Any):
        super().__init__(*args, **kwargs, content_type=self.CONTENT_TYPE)
        self["Content-Disposition"] = f"attachment; filename={filename}"
        csv_writer = csv.writer(self, delimiter=",")
        csv_writer.writerow(dataset.headers)
        csv_writer.writerows(dataset)


class ZipWithCSVFilesHttpResponse(HttpResponse):
    CONTENT_TYPE = "application/x-zip-compressed"

    def __init__(self, csv_files: list[CSVHttpResponseFromDataset], filename: str, *args: Any, **kwargs: Any):
        super().__init__(*args, **kwargs, content_type=self.CONTENT_TYPE)
        self["Content-Disposition"] = f"attachment; filename={filename}"

        z = zipfile.ZipFile(self, "w")

        for file in csv_files:
            csv_filename = file["Content-Disposition"].split("=")[-1]

            z.writestr(csv_filename, file.content)


def get_mapped_value(
    row: dict[str, Any],
    value_map: dict[str, Any],
    field_name: str,
    fields: dict[str, Any],
    *,
    optional: bool = False,
) -> Any:
    """
    Returns value from value_map by key row[field_column_name].
    """

    try:
        field_value = row[fields[field_name].column_name].strip()

        if not optional:
            return value_map[field_value]
        else:
            return value_map.get(field_value)
    except KeyError as e:
        raise Exception(f"Cannot get `{field_name}`, {str(e)}")


def get_mapped_value_by_multiple_fields(
    row: dict[str, Any],
    value_map: dict[tuple[Any, ...], Any],
    field_names: list[str],
    fields: dict[str, Any],
    *,
    optional: bool = False,
) -> Any:
    try:
        combine_value = tuple(row[fields[field_name].column_name] for field_name in field_names)

        combine_value = tuple(r.strip() if isinstance(r, str) else r for r in combine_value)

        if not optional:
            return value_map[combine_value]
        else:
            return value_map.get(combine_value)
    except KeyError as e:
        raise Exception(f"Cannot get data for `{field_names}`, {str(e)}")


def discount_partner_operators_clean(value: str, row: Optional[dict[str, str]] = None) -> str:
    if value.upper() == ALL_CHOICE_VALUE:
        if row is None:
            logging.error(f"Cannot get {AGREEMENT_PARTNER_OPERATORS_COLUMN_NAME}")
            raise ValueError(f"Cannot get {AGREEMENT_PARTNER_OPERATORS_COLUMN_NAME}")

        return row[AGREEMENT_PARTNER_OPERATORS_COLUMN_NAME]

    return value


def stringify_decimal(value: Decimal) -> Optional[str]:
    if value is None:
        return None

    return str(value)


def validate_unknown_call_destination(call_destination: str, service_types: list[ServiceTypeEnum]) -> str:
    if call_destination == CallDestinationEnum.UNKNOWN.name and not any(s.get_default_cd() for s in service_types):
        return ""

    return call_destination
