from decimal import Decimal

import pytest
from rest_framework.exceptions import ValidationError

from nga.apps.agreements.api.serializers.discount_update import (
    BaseDiscountUpdateSerializer,
    DiscountUpdateSerializer,
    SubDiscountUpdateSerializer,
)
from nga.apps.agreements.enums import DiscountDirectionEnum, DiscountModelTypeEnum


class TestBaseDiscountUpdateSerializer:
    serializer_class = BaseDiscountUpdateSerializer

    def test_schema(self):
        """Test that the serializer has all expected fields."""
        expected_fields = (
            "home_operators",
            "partner_operators",
            "direction",
            "service_types",
            "start_date",
            "end_date",
            "model_type",
            "currency_code",
            "tax_type",
            "volume_type",
            "settlement_method",
            "call_destinations",
            "called_countries",
            "traffic_segments",
            "imsi_count_type",
            "qualifying_rule",
            "inbound_market_share",
            "parameters",
        )
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(expected_fields)

    def test_all_fields_are_optional(self):
        """Test that all fields in BaseDiscountUpdateSerializer are optional."""
        s = self.serializer_class(data={})
        assert s.is_valid()

    def test_inbound_market_share_validation(self):
        """Test inbound_market_share field validation."""
        # Valid decimal value
        s = self.serializer_class(data={"inbound_market_share": "125.55"})
        assert s.is_valid()
        assert s.validated_data["inbound_market_share"] == Decimal("125.55")

        # None value
        s = self.serializer_class(data={"inbound_market_share": None})
        assert s.is_valid()
        assert s.validated_data["inbound_market_share"] is None

        # Empty data (should use default None)
        s = self.serializer_class(data={})
        assert s.is_valid()
        assert s.validated_data.get("inbound_market_share") is None


class TestDiscountUpdateSerializer:
    serializer_class = DiscountUpdateSerializer

    def test_schema(self):
        """Test that the serializer has all expected fields including financial_threshold."""
        expected_fields = (
            "home_operators",
            "partner_operators",
            "direction",
            "service_types",
            "start_date",
            "end_date",
            "model_type",
            "currency_code",
            "tax_type",
            "volume_type",
            "settlement_method",
            "call_destinations",
            "called_countries",
            "traffic_segments",
            "imsi_count_type",
            "qualifying_rule",
            "inbound_market_share",
            "parameters",
            "commitment_distribution_parameters",
            "financial_threshold",
        )
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(expected_fields)

    def test_financial_threshold_field_properties(self):
        """Test financial_threshold field properties."""
        s = self.serializer_class()
        field = s.fields["financial_threshold"]
        
        assert field.required is False
        assert field.allow_null is True
        assert field.default is None

    def test_financial_threshold_validation_valid_decimal(self):
        """Test financial_threshold accepts valid decimal values."""
        test_cases = [
            "50000.75",
            "0.00",
            "999999.99",
            "123456789.12",
        ]
        
        for value in test_cases:
            s = self.serializer_class(data={"financial_threshold": value})
            assert s.is_valid(), f"Failed for value: {value}, errors: {s.errors}"
            assert s.validated_data["financial_threshold"] == Decimal(value)

    def test_financial_threshold_validation_none(self):
        """Test financial_threshold accepts None value."""
        s = self.serializer_class(data={"financial_threshold": None})
        assert s.is_valid()
        assert s.validated_data["financial_threshold"] is None

    def test_financial_threshold_validation_default(self):
        """Test financial_threshold uses default None when not provided."""
        s = self.serializer_class(data={})
        assert s.is_valid()
        assert s.validated_data.get("financial_threshold") is None

    def test_financial_threshold_validation_invalid_values(self):
        """Test financial_threshold rejects invalid values."""
        invalid_values = [
            "invalid_decimal",
            "abc123",
            "",
            [],
            {},
        ]
        
        for value in invalid_values:
            s = self.serializer_class(data={"financial_threshold": value})
            assert not s.is_valid(), f"Should have failed for value: {value}"
            assert "financial_threshold" in s.errors

    def test_financial_threshold_precision(self):
        """Test financial_threshold handles decimal precision correctly."""
        # Test with maximum precision (18 digits, 2 decimal places for BoundDecimalField)
        s = self.serializer_class(data={"financial_threshold": "9999999999999999.99"})
        assert s.is_valid()
        assert s.validated_data["financial_threshold"] == Decimal("9999999999999999.99")

    def test_commitment_distribution_parameters_field(self):
        """Test commitment_distribution_parameters field is present and optional."""
        s = self.serializer_class()
        field = s.fields["commitment_distribution_parameters"]
        
        assert field.required is False
        assert field.allow_empty is True
        assert field.allow_null is True
        assert field.default is None

    def test_all_fields_are_optional(self):
        """Test that all fields in DiscountUpdateSerializer are optional."""
        s = self.serializer_class(data={})
        assert s.is_valid()

    def test_multiple_fields_update(self):
        """Test updating multiple fields including financial_threshold."""
        data = {
            "direction": DiscountDirectionEnum.OUTBOUND.name,
            "model_type": DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE.name,
            "currency_code": "USD",
            "financial_threshold": "75000.50",
            "inbound_market_share": "25.75",
        }
        
        s = self.serializer_class(data=data)
        assert s.is_valid(), f"Validation errors: {s.errors}"
        
        validated_data = s.validated_data
        assert validated_data["direction"] == DiscountDirectionEnum.OUTBOUND
        assert validated_data["model_type"] == DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE
        assert validated_data["currency_code"] == "USD"
        assert validated_data["financial_threshold"] == Decimal("75000.50")
        assert validated_data["inbound_market_share"] == Decimal("25.75")


class TestSubDiscountUpdateSerializer:
    serializer_class = SubDiscountUpdateSerializer

    def test_schema(self):
        """Test that the serializer has all expected fields including above_financial_threshold_rate."""
        expected_fields = (
            "home_operators",
            "partner_operators",
            "direction",
            "service_types",
            "start_date",
            "end_date",
            "model_type",
            "currency_code",
            "tax_type",
            "volume_type",
            "settlement_method",
            "call_destinations",
            "called_countries",
            "traffic_segments",
            "imsi_count_type",
            "qualifying_rule",
            "inbound_market_share",
            "parameters",
            "above_commitment_rate",
            "above_financial_threshold_rate",
        )
        s = self.serializer_class()
        assert sorted(s.fields.keys()) == sorted(expected_fields)

    def test_above_financial_threshold_rate_field_properties(self):
        """Test above_financial_threshold_rate field properties."""
        s = self.serializer_class()
        field = s.fields["above_financial_threshold_rate"]
        
        assert field.required is False
        assert field.allow_null is True
        assert field.default is None

    def test_above_financial_threshold_rate_validation(self):
        """Test above_financial_threshold_rate accepts valid decimal values."""
        test_cases = [
            "15.5678901234",
            "0.0000000000",
            "99.9999999999",
            "100.0000000000",
        ]
        
        for value in test_cases:
            s = self.serializer_class(data={"above_financial_threshold_rate": value})
            assert s.is_valid(), f"Failed for value: {value}, errors: {s.errors}"
            assert s.validated_data["above_financial_threshold_rate"] == Decimal(value)

    def test_above_financial_threshold_rate_none(self):
        """Test above_financial_threshold_rate accepts None value."""
        s = self.serializer_class(data={"above_financial_threshold_rate": None})
        assert s.is_valid()
        assert s.validated_data["above_financial_threshold_rate"] is None

    def test_above_commitment_rate_field(self):
        """Test above_commitment_rate field is present and works correctly."""
        s = self.serializer_class(data={"above_commitment_rate": "22.1340000000"})
        assert s.is_valid()
        assert s.validated_data["above_commitment_rate"] == Decimal("22.1340000000")

    def test_all_fields_are_optional(self):
        """Test that all fields in SubDiscountUpdateSerializer are optional."""
        s = self.serializer_class(data={})
        assert s.is_valid()

    def test_multiple_financial_fields_update(self):
        """Test updating both above_commitment_rate and above_financial_threshold_rate."""
        data = {
            "above_commitment_rate": "22.1340000000",
            "above_financial_threshold_rate": "15.5678901234",
        }
        
        s = self.serializer_class(data=data)
        assert s.is_valid(), f"Validation errors: {s.errors}"
        
        validated_data = s.validated_data
        assert validated_data["above_commitment_rate"] == Decimal("22.1340000000")
        assert validated_data["above_financial_threshold_rate"] == Decimal("15.5678901234")
