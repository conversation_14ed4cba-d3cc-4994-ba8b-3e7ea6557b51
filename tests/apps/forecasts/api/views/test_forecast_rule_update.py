from datetime import date, datetime
from typing import Any, Optional, Type

from django.contrib.auth.models import Anonymous<PERSON>ser, User
from django.test import RequestFactory
from django.urls import reverse_lazy
from rest_framework import status
from rest_framework.response import Response
from rest_framework.test import force_authenticate
from rest_framework.viewsets import GenericViewSet

import pytest
from nga.apps.common.consts import DATE_FORMAT
from nga.apps.common.queryset_utils import to_pk_list
from nga.apps.forecasts.api.serializers.forecast_rule_crud import ForecastRuleListSerializer
from nga.apps.forecasts.api.views import ForecastRulePatchAPIView
from nga.apps.forecasts.infra.orm import models
from nga.core.enums import PartnerTypeEnum
from tests.apps.budgets.fakes import InMemoryBudgetProvider
from tests.core.fakes import InMemoryMediator
from tests.factories.budgets import BudgetFactory, BudgetORMFactory, MasterBudgetORMFactory
from tests.factories.forecasts import ForecastRuleORMFactory
from tests.factories.references import OperatorORMFactory
from tests.utils import assert_update_budget_after_forecast_modified_command_is_sent


@pytest.mark.django_db
class TestForecastRulePatchAPIView:
    view_class = ForecastRulePatchAPIView
    serializer_class = ForecastRuleListSerializer
    url_name = "forecast_rules_partial_update"

    def test_authentication_required(self, rf):
        kwargs = dict(pk=1)
        url = reverse_lazy(self.url_name, kwargs=kwargs)

        request = rf.patch(url, data={})
        request.user = AnonymousUser()

        response = self.perform_request(request, **kwargs)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @classmethod
    def perform_request(cls, request, view_class: Type[GenericViewSet] = view_class, **kwargs) -> Response:
        return view_class.as_view()(request, **kwargs)

    def test_200_ok(self, rf: RequestFactory, staff_user: User):
        rule = ForecastRuleORMFactory(start_date=date(day=1, month=11, year=2011), volume=11)

        home_operator_ids = to_pk_list(OperatorORMFactory.create_batch(2))
        partner_operator_ids = to_pk_list(OperatorORMFactory.create_batch(2))

        data = {
            "start_date": "2022-12",
            "volume": 55,
            "home_operators": home_operator_ids,
            "partner_operators": partner_operator_ids,
        }

        response = self.patch_rule(rf, staff_user, rule.id, data)
        assert response.status_code == status.HTTP_200_OK

        patched_rule = models.ForecastRule.objects.get(pk=rule.id)

        assert patched_rule.start_date == datetime.strptime(data["start_date"], DATE_FORMAT).date()
        assert patched_rule.volume == data["volume"]
        assert sorted(to_pk_list(patched_rule.home_operators.all())) == sorted(home_operator_ids)
        assert sorted(to_pk_list(patched_rule.partner_operators.all())) == sorted(partner_operator_ids)

        assert patched_rule.end_date == rule.end_date
        assert patched_rule.service_type == rule.service_type
        assert patched_rule.traffic_direction == rule.traffic_direction
        assert patched_rule.model == rule.model
        assert patched_rule.volume_percentage == rule.volume_percentage
        assert patched_rule.partner_type == rule.partner_type
        assert patched_rule.budget_id == rule.budget_id

    @pytest.mark.parametrize(
        "partner_type,partner_param",
        [
            (PartnerTypeEnum.OPERATOR, {"partner_operators": []}),
            (PartnerTypeEnum.COUNTRY, {"partner_countries": []}),
        ],
    )
    def test_with_partners_empty_value(
        self,
        rf: RequestFactory,
        staff_user: User,
        partner_type: PartnerTypeEnum,
        partner_param: Optional[list],
    ):
        rule = ForecastRuleORMFactory(partner_type=partner_type)

        home_operator_ids = to_pk_list(OperatorORMFactory.create_batch(2))

        data = {
            "home_operators": home_operator_ids,
            **partner_param,
        }

        response = self.patch_rule(rf, staff_user, rule.id, data)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert str(response.data[0]) == "Provide values for partner field."

    def test_403_for_master_budget(self, rf: RequestFactory, staff_user: User):
        rule = ForecastRuleORMFactory(budget=MasterBudgetORMFactory())
        data = {"volume": 77777}

        response = self.patch_rule(rf, staff_user, rule.id, data)
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_update_budget_command_is_sent(
        self,
        rf: RequestFactory,
        staff_user: User,
        in_memory_mediator: InMemoryMediator,
        override_deps,
    ):
        orm_budget = BudgetORMFactory()
        budget = BudgetFactory(id=orm_budget.pk)
        budget_provider = InMemoryBudgetProvider(budgets=[budget])

        rule = ForecastRuleORMFactory(budget=orm_budget)

        with override_deps(budget_provider=budget_provider, mediator=in_memory_mediator):
            response = self.patch_rule(rf, staff_user, rule_id=rule.id, data={"volume": 77777})

        assert response.status_code == status.HTTP_200_OK

        assert_update_budget_after_forecast_modified_command_is_sent(in_memory_mediator, budget.id)

    def patch_rule(
        self,
        rf: RequestFactory,
        user: User,
        rule_id: int,
        data: dict[str, Any],
        view_class: Type[GenericViewSet] = view_class,
    ) -> Response:
        kwargs = dict(pk=rule_id)
        url = reverse_lazy(self.url_name, kwargs=kwargs)

        request = rf.patch(url, data=data, content_type="application/json")

        request.user = user
        force_authenticate(request, user)
        response = self.perform_request(request, view_class, **kwargs)

        return response
